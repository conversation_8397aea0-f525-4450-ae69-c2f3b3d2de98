@echo off
echo ========================================
echo    Fix Flutter PATH Environment
echo ========================================
echo.

echo Step 1: Testing current Flutter access...
flutter --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Flutter is already in PATH!
    flutter --version
    goto :test_app
)

echo ❌ Flutter not in PATH. Adding it now...
echo.

echo Step 2: Adding Flutter to User PATH...
powershell -Command "[Environment]::SetEnvironmentVariable('Path', [Environment]::GetEnvironmentVariable('Path', 'User') + ';C:\flutter\flutter\bin', 'User')"
echo ✅ Flutter added to User PATH

echo.
echo Step 3: Refreshing environment for current session...
set "PATH=%PATH%;C:\flutter\flutter\bin"
echo ✅ PATH refreshed for current session

echo.
echo Step 4: Testing Flutter access...
flutter --version
if %errorlevel% neq 0 (
    echo ❌ Still having issues. Using full path...
    C:\flutter\flutter\bin\flutter --version
)

:test_app
echo.
echo Step 5: Testing your InfoPulse app...
echo Installing Flutter dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo Using full path...
    C:\flutter\flutter\bin\flutter pub get
)

echo.
echo Step 6: Running Flutter Doctor...
flutter doctor
if %errorlevel% neq 0 (
    echo Using full path...
    C:\flutter\flutter\bin\flutter doctor
)

echo.
echo ========================================
echo    Setup Complete!
echo ========================================
echo.
echo Your InfoPulse app is ready!
echo.
echo To run your app:
echo   flutter run
echo.
echo Or if PATH still not working:
echo   C:\flutter\flutter\bin\flutter run
echo.
pause
