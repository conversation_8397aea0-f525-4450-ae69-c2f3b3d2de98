name: infopulse
description: A cross-platform mobile news aggregator app built with Flutter.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  provider: ^6.1.1
  
  # HTTP requests
  http: ^1.1.0
  
  # Local storage
  shared_preferences: ^2.2.2
  
  # Image caching
  cached_network_image: ^3.3.0
  
  # URL launcher
  url_launcher: ^6.2.1
  
  # Icons
  cupertino_icons: ^1.0.6
  
  # Animations
  lottie: ^2.7.0
  
  # Shimmer effect
  shimmer: ^3.0.0
  
  # Time formatting
  timeago: ^3.6.0
  
  # HTML parsing
  html: ^0.15.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/animations/
    - assets/icons/
  
  fonts:
    - family: Roboto
      fonts:
        - asset: fonts/Roboto-Regular.ttf
        - asset: fonts/Roboto-Bold.ttf
          weight: 700
