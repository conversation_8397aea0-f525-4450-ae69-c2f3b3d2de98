@echo off
echo ========================================
echo    InfoPulse News App Setup Script
echo ========================================
echo.

echo Step 1: Checking Flutter installation...
flutter doctor
if %errorlevel% neq 0 (
    echo ERROR: Flutter is not installed or not in PATH
    echo Please install Flutter first: https://flutter.dev/docs/get-started/install/windows
    pause
    exit /b 1
)

echo.
echo Step 2: Installing dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Step 3: Checking for connected devices...
flutter devices

echo.
echo Step 4: Running the app...
echo Make sure you have:
echo 1. Added your NewsAPI key to lib/utils/constants.dart
echo 2. Started an Android emulator or connected a device
echo.
pause

flutter run
