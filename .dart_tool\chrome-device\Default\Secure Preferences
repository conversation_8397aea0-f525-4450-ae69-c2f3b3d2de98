{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Google Chrome.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.49\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "fignfifoniblkonapihmkfakmlgkbkcf": {"account_extension_type": 0, "active_permissions": {"api": ["metricsPrivate", "systemPrivate", "ttsEngine", "offscreen"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"service_worker": "service_worker.js"}, "description": "Component extension providing speech via the Google network text-to-speech service.", "host_permissions": ["https://www.google.com/"], "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5mnqF6oM8Q5tYd7YqL40YL7Keftt4PwydehlNOyNlCiWDM/7SiQYwxYvVHMj1i03z7B5lZXQinrcqhHhoIgcSHK1JrdzVSJxPRVdmV0rJLv0KQgmVwL8p8MfN6SmHs+72xz+1GoRWpd0WlHMil7RzGKJA4Ku+9jxxsXoxes9eeV1hCavkb1dSF+mlQbaNiw7u1hhvc5mmeuEcWjoce8r8B2R4wmnGbuTLfoSchZ6jkasynmOaFxyT4jiYDYgrNtWRTQ/9PuPduJ+uBWVT/o2ZhDK2XcywVwzUfYIXDLDblK+YdZi8w8ZBNvc7hP9/iZr6/eoUpfsLa8qlJgyLBQebwIDAQAB", "manifest_version": 3, "name": "Google Network Speech", "permissions": ["metricsPrivate", "offscreen", "systemPrivate", "ttsEngine"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.49\\resources\\network_speech_synthesis/mv3", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "1.0"}, "serviceworkerevents": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.49\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.49\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "protection": {"macs": {"account_values": {"browser": {"show_home_button": "C176D3028D8E4EA0FC1B6BA171EF909E094853CC81EE5E873035B08781B9A5E6"}, "extensions": {"ui": {"developer_mode": "7DD3CE96EAF42AC95326689D125D7A2EBA1FBB43DB73BD904D6A012412A8CC6A"}}, "homepage": "AD81F87B9DF628F60CF7CF27150F61DAE57FB640270AA41143C73A2C3E4DD41F", "homepage_is_newtabpage": "F68D27C4CA9E4A61716EF9E6968E3B1226037B96AB6B2257F7FE7B771DC66144", "session": {"restore_on_startup": "7DC9C4E0724F8BD182E746AAE394DDF07ABB6CB223775A1CA4A2D983B3357521", "startup_urls": "51222FF121832831C93E162F3FBB359FF757FB45F4A788B7E0DF5C6154F45699"}}, "browser": {"show_home_button": "7D97D3A9BC0AFDA40464A13E522DC03E46A348FA99F89DFFA55D3C603EE9B48F"}, "default_search_provider_data": {"template_url_data": "5255E915B9AA82AF179CBE01C2CDC9D82D5FF99433D339EBA7656134C0B486A9"}, "enterprise_signin": {"policy_recovery_token": "F6B5B936B238485D6E7895D72B389A5BB0F234695ECE0C64409B6FEA0A83D228"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "8D3B31145C405EF89B1D63664991C47FF03A683415CBD0F301BEC3FD2A22A3A2", "fignfifoniblkonapihmkfakmlgkbkcf": "5105A2206434E7326954ECE89408496C6A25C44A801CE063B4E2D676C6B5F40F", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "BC9EB9CB29E7B10F842E444B98E65F2AE4C9614F0BE4B8F23E817B514E8736D5", "nkeimhogjdpnpccoofpliimaahmaaome": "7C06283838814941B49469CDCECFAEF3FA3A06752001C66A523C3BE35BCC5652"}, "ui": {"developer_mode": "1C9FD99C6E747A854FF7A3CD13D72068662989C6C56E9BCB4F872995C6CBFE47"}}, "google": {"services": {"account_id": "7464D2200BA6497E9D0264D927512B3CCC9158E898B898D393AD9EA8E48A19DC", "last_signed_in_username": "56D43A8AB75615746A5CD06CEA3FA1999B82E01A4BE464F105771EA4369DF128", "last_username": "BB43EAE3BB052B4CE9BF4C08E2C4E24987B38B7ED45A2DE98EF4F46793A1909D"}}, "homepage": "F31C55B5DC4E4BFF2A002C879A93097C2C870AC20D38F699156F30548491E206", "homepage_is_newtabpage": "9D666DEA9E1D09313FE63AB17DAD92B31B36DB9145DDDBFC4C7DAF8F0DF33978", "media": {"cdm": {"origin_data": "02A33B1D287F2E3011052837900BC3AE18130C4F78332830A7681C0D2435F124"}, "storage_id_salt": "196B7BDD13D38400761A298412AEAD9BC72A8BA3C97EA1A0FBC1B5844A710AF7"}, "module_blocklist_cache_md5_digest": "B49305E53AD22CAFBC8FA7BAE0E83E37BB8F5B1070DAF692BE2895EE937E1941", "pinned_tabs": "66EECD25235BEA55619C1D9C84572AB546C8185753A5E383EF83B3CD3D0D3FC4", "prefs": {"preference_reset_time": "67584B0DB1E44C292CE1A02145FCA7896C7C8499008E23014E0E613BE6ABF569"}, "safebrowsing": {"incidents_sent": "B918E024A8B79D5A7B21B5191679E0436BC30EC8B9D4D3CB094A75EE4AC234B5"}, "search_provider_overrides": "6C33EDE8B48CABC23550462AA2BF4607DFFD747BEF0F34A4145FD5C323D52FC4", "session": {"restore_on_startup": "F41248DB0B7F1ADEF6C8CFD3A792002CB190C8DFA258F548C4E2330A558DB030", "startup_urls": "D9EFD224FF06BCC1FED58802C3B74023120394603B7C05A58B266F16C6E3E585"}}, "super_mac": "792261BBBDA9386668ADE340B1117D0E7821508D09F74D3DE5C36699EEFAF572"}}